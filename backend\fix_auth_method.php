<?php
require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables (same as main app)
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
}

require_once __DIR__ . '/config/database.php';

use Wolffoxx\Config\Database;

try {
    // Initialize database configuration
    Database::init();
    $pdo = Database::getConnection();
    
    echo "Fixing auth_method for existing OTP users...\n";
    
    // Fix existing OTP users who don't have auth_method set
    $stmt1 = $pdo->prepare('UPDATE users SET auth_method = ? WHERE (email IS NULL OR email = "") AND phone IS NOT NULL AND phone != "" AND (auth_method IS NULL OR auth_method = "")');
    $result1 = $stmt1->execute(['phone']);
    echo "Updated users with no email: " . $stmt1->rowCount() . " rows\n";
    
    // Also update users who have both phone and email but were created via OTP
    // (These would be users who added email later via profile update)
    $stmt2 = $pdo->prepare('UPDATE users SET auth_method = ? WHERE phone IS NOT NULL AND phone != "" AND (auth_method IS NULL OR auth_method = "") AND created_at >= "2024-01-01"');
    $result2 = $stmt2->execute(['phone']);
    echo "Updated users with phone (recent): " . $stmt2->rowCount() . " rows\n";
    
    // Check the results
    $stmt3 = $pdo->query('SELECT id, phone, email, auth_method, created_at FROM users WHERE auth_method = "phone" ORDER BY created_at DESC LIMIT 10');
    $users = $stmt3->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nOTP Users:\n";
    foreach ($users as $user) {
        echo "ID: {$user['id']}, Phone: {$user['phone']}, Email: " . ($user['email'] ?: 'NULL') . ", Auth: {$user['auth_method']}, Created: {$user['created_at']}\n";
    }
    
    echo "\nDone!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
