-- Fix existing OTP users who don't have auth_method set
-- These are users who have phone numbers but no email or have email as NULL

UPDATE users 
SET auth_method = 'phone' 
WHERE (email IS NULL OR email = '') 
  AND phone IS NOT NULL 
  AND phone != ''
  AND (auth_method IS NULL OR auth_method = '');

-- Also update users who have both phone and email but were created via OTP
-- (These would be users who added email later via profile update)
UPDATE users 
SET auth_method = 'phone' 
WHERE phone IS NOT NULL 
  AND phone != ''
  AND (auth_method IS NULL OR auth_method = '')
  AND created_at >= '2024-01-01'  -- Adjust date as needed
  AND (password IS NULL OR password = '');  -- OTP users don't have passwords

-- Check the results
SELECT id, phone, email, auth_method, created_at 
FROM users 
WHERE auth_method = 'phone' 
ORDER BY created_at DESC 
LIMIT 10;
