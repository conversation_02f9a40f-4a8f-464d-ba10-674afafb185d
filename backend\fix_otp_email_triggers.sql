-- Fix OTP email requirement issue by updating database triggers
-- This script removes the strict email requirement for phone-based authentication

USE wolffoxx;

-- Drop existing triggers that enforce email requirement
DROP TRIGGER IF EXISTS users_before_insert;
DROP TRIGGER IF EXISTS users_before_update;

-- Create new triggers that allow email to be NULL for phone-based auth
DELIMITER $$

CREATE TRIGGER users_before_insert
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    -- Set default auth_method if not provided
    IF NEW.auth_method IS NULL THEN
        SET NEW.auth_method = 'email';
    END IF;
    
    -- Only require email if auth_method is 'email'
    IF NEW.auth_method = 'email' AND (NEW.email IS NULL OR NEW.email = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Email is required for email authentication';
    END IF;
    
    -- Only require phone if auth_method is 'phone'
    IF NEW.auth_method = 'phone' AND (NEW.phone IS NULL OR NEW.phone = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Phone number is required for phone authentication';
    END IF;
    
    -- Allow first_name and last_name to be NULL initially for OTP flow
    -- They can be set later via profile completion
END$$

CREATE TRIGGER users_before_update
BEFORE UPDATE ON users
FOR EACH ROW
BEGIN
    -- Only require email if auth_method is 'email'
    IF NEW.auth_method = 'email' AND (NEW.email IS NULL OR NEW.email = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Email is required for email authentication';
    END IF;
    
    -- Only require phone if auth_method is 'phone'
    IF NEW.auth_method = 'phone' AND (NEW.phone IS NULL OR NEW.phone = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Phone number is required for phone authentication';
    END IF;
    
    -- Allow first_name and last_name to be NULL initially for OTP flow
    -- They can be set later via profile completion
END$$

DELIMITER ;

-- Verify the triggers were created
SHOW TRIGGERS LIKE 'users';

SELECT 'Database triggers updated successfully! OTP registration should now work without email requirement.' as message;
