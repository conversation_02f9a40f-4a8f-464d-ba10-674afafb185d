import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { toast } from 'react-toastify';
import { motion } from 'framer-motion';
import { ArrowLeft, MapPin, CreditCard } from 'lucide-react';

const CheckoutAddressPage = () => {
  const { user, updateProfile, isLoading } = useAuth();
  const { items, subtotal, migrating, refreshCart } = useCart();
  const navigate = useNavigate();
  const location = useLocation();

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    shipping_address: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'India'
    }
  });

  const [fieldErrors, setFieldErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [hasCheckedCart, setHasCheckedCart] = useState(false);
  const [hasRefreshedCart, setHasRefreshedCart] = useState(false);

  // Calculate totals from cart items
  const calculateSubtotal = () => {
    // Use cart context subtotal if available, otherwise calculate manually
    if (subtotal && subtotal > 0) return subtotal;

    if (!items || items.length === 0) return 0;
    return items.reduce((sum, item) => {
      // Use sale price if available, otherwise use regular price (matching cart context logic)
      const effectivePrice = (item.is_sale === 1 && (item.sale_price || item.salePrice))
        ? parseFloat(item.sale_price || item.salePrice)
        : parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 1;
      return sum + (effectivePrice * quantity);
    }, 0);
  };

  const calculateTotal = () => {
    // For now, total = subtotal (no additional fees like shipping, taxes, etc.)
    return calculateSubtotal();
  };

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      setFormData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        shipping_address: {
          line1: user.shipping_address?.line1 || user.shipping_address?.address_line_1 || '',
          line2: user.shipping_address?.line2 || user.shipping_address?.address_line_2 || '',
          city: user.shipping_address?.city || '',
          state: user.shipping_address?.state || '',
          postal_code: user.shipping_address?.postal_code || '',
          country: user.shipping_address?.country || 'India'
        }
      });
    }
  }, [user]);

  // Force cart refresh when page loads to ensure we have latest data
  useEffect(() => {
    if (!hasRefreshedCart && !migrating && user?.id) {
      console.log('🔄 Forcing cart refresh in CheckoutAddressPage...');
      setHasRefreshedCart(true);
      refreshCart().then(() => {
        console.log('✅ Cart refreshed in CheckoutAddressPage');
      }).catch(error => {
        console.error('❌ Failed to refresh cart in CheckoutAddressPage:', error);
      });
    }
  }, [hasRefreshedCart, migrating, user?.id, refreshCart]);

  // Show migration status and redirect if cart is empty (but wait for migration to complete)
  useEffect(() => {
    if (migrating) {
      console.log('🔄 Cart migration in progress in CheckoutAddressPage');
      return;
    }

    // Wait longer for cart state to stabilize after migration
    const checkCartWithDelay = setTimeout(() => {
      console.log('🔍 Checking cart in CheckoutAddressPage:', {
        itemsLength: items?.length,
        migrating,
        hasItems: !!(items && items.length > 0),
        hasCheckedCart
      });

      if (!hasCheckedCart) {
        setHasCheckedCart(true);

        if (!items || items.length === 0) {
          // Try one more time with a longer delay before giving up
          console.log('⚠️ Cart appears empty, trying one more time...');
          setTimeout(() => {
            if (!items || items.length === 0) {
              console.log('⚠️ Cart is still empty after final check, redirecting to cart');
              toast.error('Your cart appears to be empty. Please add items and try again.');
              navigate('/cart');
            } else {
              console.log('✅ Cart has items after final check:', items.length);
            }
          }, 1000);
        } else {
          console.log('✅ Cart has items in CheckoutAddressPage:', items.length);
        }
      }
    }, 1000); // Increased delay to 1000ms to allow state updates

    return () => clearTimeout(checkCartWithDelay);
  }, [items, navigate, migrating]);

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    // Basic profile validation
    if (!formData.first_name?.trim()) errors.first_name = 'First name is required';
    if (!formData.last_name?.trim()) errors.last_name = 'Last name is required';
    if (!formData.email?.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    if (!formData.phone?.trim()) errors.phone = 'Phone number is required';

    // Address validation - all fields required for checkout
    if (!formData.shipping_address.line1?.trim()) errors.line1 = 'Address Line 1 is required';
    if (!formData.shipping_address.city?.trim()) errors.city = 'City is required';
    if (!formData.shipping_address.state?.trim()) errors.state = 'State is required';
    if (!formData.shipping_address.postal_code?.trim()) errors.postal_code = 'Postal Code is required';
    if (!formData.shipping_address.country?.trim()) errors.country = 'Country is required';

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSaving(true);

    try {
      // Prepare data for profile update (matches ProfilePage logic)
      const updateData = {
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        email: formData.email.trim(),
        address_data: {
          first_name: formData.first_name.trim(),
          last_name: formData.last_name.trim(),
          address_line_1: formData.shipping_address.line1.trim(),
          address_line_2: formData.shipping_address.line2?.trim() || '',
          city: formData.shipping_address.city.trim(),
          state: formData.shipping_address.state.trim(),
          postal_code: formData.shipping_address.postal_code.trim(),
          country: formData.shipping_address.country.trim(),
          type: 'shipping',
          is_default: true
        }
      };

      // Only include phone if it's different from the user's current phone (for OTP users)
      if (formData.phone.trim() && formData.phone.trim() !== user?.phone) {
        updateData.phone = formData.phone.trim();
      }

      console.log('🔄 Sending profile update data:', updateData);
      console.log('🔄 User auth method:', user?.auth_method);

      const result = await updateProfile(updateData);

      if (result.success) {
        toast.success('Address saved successfully!');

        // Wait for any ongoing cart migration to complete before redirecting
        const waitForCartMigration = async () => {
          // Wait for migration to complete if in progress
          let attempts = 0;
          const maxAttempts = 30; // 3 seconds max wait
          while (migrating && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
          }

          // Additional delay to ensure profile and cart state are updated
          await new Promise(resolve => setTimeout(resolve, 500));

          console.log('✅ Address saved, redirecting to cart for checkout');
          navigate('/cart', {
            state: {
              fromAddressForm: true,
              proceedToCheckout: true
            }
          });
        };

        waitForCartMigration();
      } else {
        // Handle validation errors from backend
        if (result.validation_errors) {
          const backendErrors = {};
          Object.entries(result.validation_errors).forEach(([field, messages]) => {
            const errorMessage = Array.isArray(messages) ? messages[0] : messages;
            
            if (field.startsWith('address_data.')) {
              const addressField = field.replace('address_data.', '');
              const frontendField = addressField === 'address_line_1' ? 'line1' :
                                  addressField === 'address_line_2' ? 'line2' :
                                  addressField;
              backendErrors[frontendField] = errorMessage;
            } else {
              backendErrors[field] = errorMessage;
            }
          });
          
          setFieldErrors(backendErrors);
          toast.error('Please correct the errors below');
        } else {
          toast.error(result.message || 'Failed to save address');
        }
      }
    } catch (error) {
      console.error('Address save error:', error);
      toast.error('An error occurred while saving your address');
    } finally {
      setIsSaving(false);
    }
  };

  const handleBackToCart = () => {
    navigate('/cart');
  };

  // Show loading state during cart migration
  if (migrating) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-[#2a2a2a] p-6 rounded-full">
              <div className="w-8 h-8 border-2 border-[#FF6F35] border-t-transparent rounded-full animate-spin" />
            </div>
          </div>
          <h2 className="text-xl sm:text-2xl font-bold text-white mb-4">
            Preparing Your Cart
          </h2>
          <p className="text-[#AAAAAA] text-sm sm:text-base max-w-md mx-auto">
            We're setting up your cart items. This will just take a moment...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <button
            onClick={handleBackToCart}
            className="flex items-center gap-2 text-gray-400 hover:text-white mb-4 transition-colors"
          >
            <ArrowLeft size={20} />
            Back to Cart
          </button>
          
          <div className="flex items-center gap-3 mb-2">
            <MapPin className="text-[#FF6F35]" size={24} />
            <h1 className="text-2xl font-bold">Shipping Address</h1>
          </div>
          <p className="text-gray-400">Complete your address to proceed with checkout</p>
        </motion.div>

        {/* Order Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[#1a1a1a] border border-gray-700 rounded-lg p-6 mb-6"
        >
          <h3 className="text-white font-semibold text-lg mb-6">Order Summary</h3>

          {/* Individual Items */}
          <div className="space-y-4 mb-6">
            {items?.map((item, index) => {
              // Use sale price if available, otherwise use regular price (matching cart logic)
              const effectivePrice = parseFloat(item.salePrice || item.sale_price || item.price) || 0;
              const quantity = parseInt(item.quantity) || 1;
              const itemTotal = effectivePrice * quantity;

              return (
                <div key={`${item.id}-${item.color}-${item.size}-${index}`} className="flex gap-3 sm:gap-4 p-3 sm:p-4 bg-[#2a2a2a] rounded-lg hover:bg-[#333333] transition-colors">
                  {/* Item Image */}
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-800 rounded-lg overflow-hidden flex-shrink-0 shadow-lg">
                    {item.image ? (
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className="w-full h-full bg-gray-700 flex items-center justify-center text-gray-400 text-xs" style={{display: item.image ? 'none' : 'flex'}}>
                      No Image
                    </div>
                  </div>

                  {/* Item Details */}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-medium text-sm sm:text-base mb-2 leading-tight">
                      {/* Mobile: Show shorter name, Desktop: Show longer name */}
                      <span className="sm:hidden">
                        {item.name?.length > 20 ? `${item.name.substring(0, 20)}...` : item.name || 'Product'}
                      </span>
                      <span className="hidden sm:block">
                        {item.name?.length > 35 ? `${item.name.substring(0, 35)}...` : item.name || 'Product'}
                      </span>
                    </h4>

                    {/* Mobile: Stack vertically, Desktop: Horizontal */}
                    <div className="space-y-1 sm:space-y-0 sm:flex sm:items-center sm:gap-3 text-xs sm:text-sm text-[#AAAAAA]">
                      {item.color && (
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full border border-[#404040] flex-shrink-0"
                            style={{
                              backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                              opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                            }}
                          ></div>
                          <span>{item.color}</span>
                        </div>
                      )}
                      {item.size && (
                        <div className="flex items-center gap-2">
                          <span>Size: {item.size}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <span>Qty: {quantity}</span>
                      </div>
                    </div>
                  </div>

                  {/* Item Price */}
                  <div className="text-right flex-shrink-0 min-w-[70px] sm:min-w-[80px]">
                    <div className="text-white font-semibold text-base sm:text-lg">₹{itemTotal.toFixed(2)}</div>
                    {quantity > 1 && (
                      <div className="text-xs text-[#AAAAAA] mt-1">₹{effectivePrice.toFixed(2)} each</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Totals */}
          <div className="border-t border-gray-700 pt-4 space-y-3">
            <div className="flex justify-between text-base text-[#AAAAAA]">
              <span>Subtotal ({items?.length || 0} item{(items?.length || 0) !== 1 ? 's' : ''})</span>
              <span>₹{calculateSubtotal().toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm text-[#AAAAAA]">
              <span>Shipping</span>
              <span className="text-green-400 font-medium">Free</span>
            </div>
            <div className="border-t border-gray-700 pt-3">
              <div className="flex justify-between font-semibold text-xl">
                <span className="text-white">Total</span>
                <span className="text-[#FF6F35]">₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Address Form */}
        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          onSubmit={handleSubmit}
          className="bg-[#1a1a1a] border border-gray-700 rounded-lg p-6"
        >
          {/* Personal Information */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  First Name <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.first_name ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter first name"
                />
                {fieldErrors.first_name && <p className="text-red-400 text-xs mt-1">{fieldErrors.first_name}</p>}
              </div>

              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Last Name <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.last_name ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter last name"
                />
                {fieldErrors.last_name && <p className="text-red-400 text-xs mt-1">{fieldErrors.last_name}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Email <span className="text-red-400">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.email ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter email address"
                />
                {fieldErrors.email && <p className="text-red-400 text-xs mt-1">{fieldErrors.email}</p>}
              </div>

              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Phone Number <span className="text-red-400">*</span>
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.phone ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter phone number"
                />
                {fieldErrors.phone && <p className="text-red-400 text-xs mt-1">{fieldErrors.phone}</p>}
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">Shipping Address</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Address Line 1 <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.shipping_address.line1}
                  onChange={(e) => handleInputChange('shipping_address.line1', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.line1 ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Street address"
                />
                {fieldErrors.line1 && <p className="text-red-400 text-xs mt-1">{fieldErrors.line1}</p>}
              </div>

              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Address Line 2
                </label>
                <input
                  type="text"
                  value={formData.shipping_address.line2}
                  onChange={(e) => handleInputChange('shipping_address.line2', e.target.value)}
                  className="w-full px-4 py-3 bg-black border border-gray-600 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all"
                  placeholder="Apartment, suite, etc. (optional)"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    City <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.shipping_address.city}
                    onChange={(e) => handleInputChange('shipping_address.city', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.city ? 'border-red-500' : 'border-gray-600'}`}
                    placeholder="Enter city"
                  />
                  {fieldErrors.city && <p className="text-red-400 text-xs mt-1">{fieldErrors.city}</p>}
                </div>

                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    State <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.shipping_address.state}
                    onChange={(e) => handleInputChange('shipping_address.state', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.state ? 'border-red-500' : 'border-gray-600'}`}
                    placeholder="Enter state"
                  />
                  {fieldErrors.state && <p className="text-red-400 text-xs mt-1">{fieldErrors.state}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    Postal Code <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.shipping_address.postal_code}
                    onChange={(e) => handleInputChange('shipping_address.postal_code', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.postal_code ? 'border-red-500' : 'border-gray-600'}`}
                    placeholder="Enter postal code"
                  />
                  {fieldErrors.postal_code && <p className="text-red-400 text-xs mt-1">{fieldErrors.postal_code}</p>}
                </div>

                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    Country <span className="text-red-400">*</span>
                  </label>
                  <select
                    value={formData.shipping_address.country}
                    onChange={(e) => handleInputChange('shipping_address.country', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.country ? 'border-red-500' : 'border-gray-600'}`}
                  >
                    <option value="India">India</option>
                  </select>
                  {fieldErrors.country && <p className="text-red-400 text-xs mt-1">{fieldErrors.country}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSaving || isLoading || migrating}
            className="w-full bg-[#FF6F35] hover:bg-[#FF6F35]/90 text-white font-medium py-4 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {migrating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Preparing Cart...
              </>
            ) : isSaving ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Saving Address...
              </>
            ) : (
              <>
                <CreditCard size={20} />
                Continue to Payment
              </>
            )}
          </button>
        </motion.form>
      </div>
    </div>
  );
};

export default CheckoutAddressPage;
