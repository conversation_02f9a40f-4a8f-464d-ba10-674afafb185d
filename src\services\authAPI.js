// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// API Error Class
class APIError extends Error {
  constructor(message, status, data = null) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.data = data;
  }
}

// HTTP Client with error handling
const httpClient = {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add auth token if provided
    if (options.token) {
      config.headers.Authorization = `Bearer ${options.token}`;
    }

    try {
      const response = await fetch(url, config);

      // Parse response
      let data;
      const contentType = response.headers.get('content-type');
      

      
      // Clone the response before reading the body to avoid the "already used" error
      const responseClone = response.clone();
      
      if (contentType && contentType.includes('application/json')) {
        try {
          data = await response.json();
        } catch (parseError) {
          // If JSON parsing fails, get the text from the cloned response
          try {
            const textData = await responseClone.text();
            throw new APIError(`Invalid JSON response: ${parseError.message}`, response.status, textData);
          } catch (textError) {
            // If even getting text fails, throw the original error
            throw new APIError(`Invalid JSON response: ${parseError.message}`, response.status);
          }
        }
      } else {
        try {
          data = await response.text();

          // Check if this is a PHP error
          if (data.includes('Fatal error') || data.includes('<br />') || data.includes('<b>')) {
            // Extract the actual error message if possible
            const errorMatch = data.match(/Fatal error: (.*?) in/);
            if (errorMatch && errorMatch[1]) {
              throw new APIError(`Server error: ${errorMatch[1]}`, response.status, data);
            } else {
              throw new APIError('Server error occurred. Please try again later.', response.status, data);
            }
          }
        } catch (textError) {
          if (textError instanceof APIError) {
            throw textError; // Re-throw our custom error
          }
          throw new APIError(`Failed to read response: ${textError.message}`, response.status);
        }
      }

      // Handle HTTP errors
      if (!response.ok) {
        // Try to extract a meaningful error message
        let errorMessage;

        if (data && typeof data === 'object') {
          // If we have a structured error response
          errorMessage = data.message || data.error || `HTTP ${response.status}: ${response.statusText}`;
        } else if (typeof data === 'string' && data.includes('Fatal error')) {
          // If we have a PHP error message
          errorMessage = 'Server error occurred. Please try again later.';
        } else {
          // Default error message
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }

        const error = new APIError(errorMessage, response.status, data);
        throw error;
      }

      // Return data from successful response
      return data?.data || data;

    } catch (error) {
      // Network or parsing errors
      if (error instanceof APIError) {
        throw error;
      }

      // Network error
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new APIError('Network error. Please check your connection.', 0);
      }

      // JSON parsing error (already handled above)
      if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
        throw new APIError(`Invalid JSON response: ${error.message}`, 0);
      }

      // Other errors
      throw new APIError(error.message || 'An unexpected error occurred', 0);
    }
  },

  get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  },

  post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
  },

  put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
  },

  delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options });
  },
};

// Phone number formatting utility
const formatPhoneNumber = (phone) => {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');

  // Add country code if missing (assuming India +91)
  if (cleaned.length === 10) {
    return `91${cleaned}`;
  }

  return cleaned;
};

// Validation utilities
const validatePhone = (phone) => {
  // Guard against empty/null/undefined phone
  if (!phone || phone.trim() === '') {
    return false;
  }

  const cleaned = phone.replace(/\D/g, '');

  // Handle both formats:
  // 1. 10-digit Indian mobile number (6-9 followed by 9 digits)
  // 2. 12-digit with country code (91 followed by 10-digit mobile number)
  if (cleaned.length === 10) {
    // Standard 10-digit format
    const firstDigit = cleaned.charAt(0);
    return ['6', '7', '8', '9'].includes(firstDigit);
  } else if (cleaned.length === 12) {
    // 12-digit format with country code
    if (cleaned.startsWith('91')) {
      const mobileNumber = cleaned.substring(2); // Remove country code
      const firstDigit = mobileNumber.charAt(0);
      return ['6', '7', '8', '9'].includes(firstDigit);
    }
    return false;
  }

  return false;
};

const validateOTP = (otp) => {
  return /^\d{4,8}$/.test(otp);
};

// Auth API Service
export const authAPI = {
  /**
   * Email/Password Login
   */
  async login(credentials) {
    try {
      const response = await httpClient.post('/auth/login', credentials);
      return response;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Email/Password Registration
   */
  async register(userData) {
    try {
      const response = await httpClient.post('/auth/register', userData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Forgot Password
   */
  async forgotPassword(email) {
    const requestId = `forgot_pwd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      console.log('🔐 FORGOT_PASSWORD_START', {
        requestId,
        email,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });

      console.log('🔐 FORGOT_PASSWORD_VALIDATION', {
        requestId,
        emailProvided: !!email,
        emailLength: email ? email.length : 0,
        emailFormat: email ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) : false
      });

      console.log('🔐 FORGOT_PASSWORD_API_CALL_START', {
        requestId,
        endpoint: '/auth/forgot-password',
        method: 'POST',
        payload: { email }
      });

      const response = await httpClient.post('/auth/forgot-password', { email });

      console.log('🔐 FORGOT_PASSWORD_API_RESPONSE', {
        requestId,
        success: true,
        status: response.status || 'unknown',
        message: response.message || 'unknown',
        hasData: !!response.data
      });

      console.log('🔐 FORGOT_PASSWORD_SUCCESS', {
        requestId,
        email,
        completed: true
      });

      return response;
    } catch (error) {
      console.error('🔐 FORGOT_PASSWORD_ERROR', {
        requestId,
        email,
        errorMessage: error.message,
        errorStatus: error.status,
        errorData: error.data,
        errorStack: error.stack
      });
      throw error;
    }
  },

  /**
   * Reset Password
   */
  async resetPassword(token, password, confirmPassword) {
    const requestId = `reset_pwd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    try {
      console.log('🔐 RESET_PASSWORD_START', {
        requestId,
        tokenProvided: !!token,
        tokenLength: token ? token.length : 0,
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
        passwordProvided: !!password,
        passwordLength: password ? password.length : 0,
        confirmPasswordProvided: !!confirmPassword,
        passwordsMatch: password === confirmPassword,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });

      console.log('🔐 RESET_PASSWORD_VALIDATION', {
        requestId,
        tokenValid: token && token.length > 10,
        passwordValid: password && password.length >= 8,
        confirmPasswordValid: confirmPassword && confirmPassword.length >= 8,
        passwordsMatch: password === confirmPassword
      });

      const payload = {
        token,
        password,
        password_confirmation: confirmPassword
      };

      console.log('🔐 RESET_PASSWORD_API_CALL_START', {
        requestId,
        endpoint: '/auth/reset-password',
        method: 'POST',
        payloadKeys: Object.keys(payload),
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided'
      });

      const response = await httpClient.post('/auth/reset-password', payload);

      console.log('🔐 RESET_PASSWORD_API_RESPONSE', {
        requestId,
        success: true,
        status: response.status || 'unknown',
        message: response.message || 'unknown',
        hasData: !!response.data
      });

      console.log('🔐 RESET_PASSWORD_SUCCESS', {
        requestId,
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
        completed: true
      });

      return response;
    } catch (error) {
      console.error('🔐 RESET_PASSWORD_ERROR', {
        requestId,
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
        errorMessage: error.message,
        errorStatus: error.status,
        errorData: error.data,
        errorStack: error.stack
      });
      throw error;
    }
  },
  /**
   * Send OTP to phone number
   */
  async sendOTP(phone, purpose = 'login') {
    // Guard against empty phone
    if (!phone || phone.trim() === '') {
      throw new APIError('Phone number is required');
    }

    // Validate phone number
    if (!validatePhone(phone)) {
      throw new APIError('Please enter a valid phone number');
    }

    // Send raw phone number - let backend handle formatting
    const cleanPhone = phone.replace(/\D/g, '');

    try {
      const response = await httpClient.post('/auth/otp/send', {
        phone: cleanPhone,
        purpose
      });

      return {
        phone: response.phone,
        message: response.message,
        expires_in: response.expires_in,
        can_resend_after: response.can_resend_after
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Verify OTP and login/register
   */
  async verifyOTP(phone, otp, userDetails = {}) {
    // Validate inputs
    if (!validatePhone(phone)) {
      throw new APIError('Please enter a valid phone number');
    }

    if (!validateOTP(otp)) {
      throw new APIError('Please enter a valid OTP');
    }

    // Send raw phone number - let backend handle formatting
    const cleanPhone = phone.replace(/\D/g, '');

    try {
      const response = await httpClient.post('/auth/otp/verify', {
        phone: cleanPhone,
        otp,
        user_details: userDetails
      });

      return {
        user: response.user,
        tokens: response.tokens,
        is_new_user: response.is_new_user,
        message: response.message
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Resend OTP
   */
  async resendOTP(phone, purpose = 'login') {
    // Guard against empty phone
    if (!phone || phone.trim() === '') {
      throw new APIError('Phone number is required');
    }

    // Validate phone number
    if (!validatePhone(phone)) {
      throw new APIError('Please enter a valid phone number');
    }

    // Send raw phone number - let backend handle formatting
    const cleanPhone = phone.replace(/\D/g, '');

    try {
      const response = await httpClient.post('/auth/otp/resend', {
        phone: cleanPhone,
        purpose
      });

      return {
        phone: response.phone,
        message: 'OTP resent successfully',
        expires_in: response.expires_in,
        can_resend_after: response.can_resend_after
      };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update user profile
   */
  async updateProfile(profileData, token, userAuthMethod = null, userData = null) {


    if (!token) {
      throw new APIError('Authentication required');
    }

    try {
      // Determine the correct endpoint based on auth method
      // For phone/OTP users, use the OTP-specific endpoint which has more flexible validation
      let endpoint = '/profile'; // Default to ProfileController

      if (userAuthMethod === 'phone') {
        endpoint = '/auth/otp/profile';
      } else if (!userAuthMethod && userData) {
        // Fallback: If auth_method is not set but user has phone and no password, likely OTP user
        if (userData.phone && !userData.password) {
          endpoint = '/auth/otp/profile';
          console.log('🔄 Profile update - Detected OTP user via fallback logic');
        }
      }

      console.log('🔄 Profile update - Auth method:', userAuthMethod);
      console.log('🔄 Profile update - Using endpoint:', endpoint);
      console.log('🔄 Profile update - Data:', profileData);

      const response = await httpClient.put(endpoint, profileData, {
        token
      });

      return {
        success: true,
        profile: response.profile,
        message: response.message
      };
    } catch (error) {
      // If this is a validation error, include the validation errors in the error object
      if (error.status === 422 && error.data) {
        // Include the validation errors in the error object
        error.validation_errors = error.data.errors || error.data.validation_errors || {};
      }

      // Create an enhanced error with all the original error's properties
      const enhancedError = new APIError(
        error.data?.message || error.message || 'Failed to update profile',
        error.status,
        error.data
      );
      
      // Copy all properties from the original error
      Object.keys(error).forEach(key => {
        if (!(key in enhancedError)) {
          enhancedError[key] = error[key];
        }
      });
      

      throw enhancedError;
    }
  },

  /**
   * Get user profile
   */
  async getProfile(token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    try {
      const response = await httpClient.get('/users/profile', {
        token
      });

      return response;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Logout user
   */
  async logout(token) {
    if (!token) {
      return; // No token, nothing to logout
    }

    try {
      await httpClient.post('/auth/logout', {}, {
        token
      });
    } catch (error) {
      // Don't throw error for logout - we'll clear local data anyway
    }
  },

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken) {
    if (!refreshToken) {
      throw new APIError('Refresh token required');
    }

    try {
      // Check if refresh token is valid format before sending
      if (typeof refreshToken !== 'string' || refreshToken.length < 10) {
        throw new APIError('Invalid refresh token format');
      }
      
      const response = await httpClient.post('/auth/refresh', {
        refresh_token: refreshToken
      });
      
      // Validate response has required fields
      if (!response.access_token) {
        throw new APIError('Invalid refresh response: missing access_token');
      }

      // Calculate token expiration time
      const expiresIn = response.expires_in || 18000; // Default to 5 hours
      const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();
      
      return {
        access_token: response.access_token,
        refresh_token: response.refresh_token || refreshToken, // Use old refresh token if new one not provided
        expires_in: expiresIn, // Default to 1 hour if not specified
        expires_at: expiresAt // Add expiration timestamp
      };
    } catch (error) {
      
      // Add more context to the error
      if (error.status === 401) {
        throw new APIError('Refresh token expired or invalid. Please log in again.', 401);
      } else if (error.status === 500) {
        throw new APIError('Server error during token refresh. Please try again later.', 500);
      } else if (error.message && error.message.includes('JSON')) {
        throw new APIError('Invalid server response during token refresh. Please log in again.', 400);
      }
      
      throw error;
    }
  },

  /**
   * Upload profile image
   */
  async uploadProfileImage(imageFile, token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    if (!imageFile) {
      throw new APIError('Image file required');
    }

    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await httpClient.request('/users/profile/image', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          // Don't set Content-Type for FormData - browser will set it with boundary
        },
        body: formData
      });

      return response;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Delete profile image
   */
  async deleteProfileImage(token) {
    if (!token) {
      throw new APIError('Authentication required');
    }

    try {
      const response = await httpClient.delete('/users/profile/image', {
        token
      });

      return response;
    } catch (error) {
      throw error;
    }
  }
};

// Export utilities for use in components
export { validatePhone, validateOTP, formatPhoneNumber, APIError };
