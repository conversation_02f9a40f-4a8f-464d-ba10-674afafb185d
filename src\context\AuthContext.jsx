import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/authAPI';
import { useNotification } from './NotificationContext';

// Auth Context
const AuthContext = createContext();

// Auth Actions
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SEND_OTP_START: 'SEND_OTP_START',
  SEND_OTP_SUCCESS: 'SEND_OTP_SUCCESS',
  SEND_OTP_FAILURE: 'SEND_OTP_FAILURE',
  VERIFY_OTP_START: 'VERIFY_OTP_START',
  VERIFY_OTP_SUCCESS: 'VERIFY_OTP_SUCCESS',
  VERIFY_OTP_FAILURE: 'VERIFY_OTP_FAILURE',
  UPDATE_PROFILE_START: 'UPDATE_PROFILE_START',
  UPDATE_PROFILE_SUCCESS: 'UPDATE_PROFILE_SUCCESS',
  UPDATE_PROFILE_FAILURE: 'UPDATE_PROFILE_FAILURE',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_LOADING: 'SET_LOADING'
};

// Initial State
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  otpSent: false,
  otpPhone: null,
  originalPhone: null, // Store the original phone number for verification
  tokens: {
    accessToken: null,
    refreshToken: null
  }
};

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
    case AUTH_ACTIONS.SEND_OTP_START:
    case AUTH_ACTIONS.VERIFY_OTP_START:
    case AUTH_ACTIONS.UPDATE_PROFILE_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case AUTH_ACTIONS.SEND_OTP_SUCCESS:
      return {
        ...state,
        isLoading: false,
        otpSent: true,
        otpPhone: action.payload.phone,
        originalPhone: action.payload.originalPhone, // Store original phone
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
    case AUTH_ACTIONS.VERIFY_OTP_SUCCESS:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        tokens: action.payload.tokens,
        otpSent: false,
        otpPhone: null,
        originalPhone: null,
        error: null
      };

    case AUTH_ACTIONS.UPDATE_PROFILE_SUCCESS:
      return {
        ...state,
        isLoading: false,
        user: action.payload.profile,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
    case AUTH_ACTIONS.SEND_OTP_FAILURE:
    case AUTH_ACTIONS.VERIFY_OTP_FAILURE:
    case AUTH_ACTIONS.UPDATE_PROFILE_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload.error,
        otpSent: action.type === AUTH_ACTIONS.SEND_OTP_FAILURE ? false : state.otpSent
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    default:
      return state;
  }
};

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const { error: showError, warning: showWarning } = useNotification();

  // Validate if a token is a proper JWT
  const isValidJWT = (token) => {
    if (!token || typeof token !== 'string') return false;

    // JWT tokens should have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    try {
      // Try to decode the header and payload
      const header = JSON.parse(atob(parts[0]));
      const payload = JSON.parse(atob(parts[1]));

      // Check if it has required JWT fields
      return header.alg && payload.exp && payload.iat;
    } catch (e) {
      return false;
    }
  };

  // Load user from localStorage on app start
  useEffect(() => {
    const loadStoredAuth = () => {
      try {
        const storedTokens = localStorage.getItem('wolffoxx_tokens');
        const storedUser = localStorage.getItem('wolffoxx_user');

        console.log('🔄 Initializing auth:', {
          hasStoredTokens: !!storedTokens,
          hasStoredUser: !!storedUser
        });

        if (storedTokens && storedUser) {
          const tokens = JSON.parse(storedTokens);
          const user = JSON.parse(storedUser);

          // Get the access token
          const accessToken = tokens.access_token || tokens.accessToken;

          console.log('🔄 Parsed stored data:', {
            hasUser: !!user,
            userId: user?.id,
            hasAccessToken: !!accessToken
          });

          // Validate that it's a proper JWT token
          if (accessToken && isValidJWT(accessToken) && user) {
            // Normalize token format
            const normalizedTokens = {
              accessToken: tokens.accessToken || tokens.access_token,
              refreshToken: tokens.refreshToken || tokens.refresh_token,
              access_token: tokens.access_token || tokens.accessToken,
              refresh_token: tokens.refresh_token || tokens.refreshToken
            };

            console.log('✅ Valid auth data found, logging in user');
            dispatch({
              type: AUTH_ACTIONS.LOGIN_SUCCESS,
              payload: { user, tokens: normalizedTokens }
            });
          } else {
            console.log('❌ Invalid auth data, clearing storage');
            // Clear invalid/corrupted tokens
            localStorage.removeItem('wolffoxx_tokens');
            localStorage.removeItem('wolffoxx_user');
          }
        } else {
          console.log('🔄 No stored auth data found');
        }
      } catch (error) {
        console.error('❌ Error initializing auth:', error);
        // Clear invalid stored data
        localStorage.removeItem('wolffoxx_tokens');
        localStorage.removeItem('wolffoxx_user');
      }
    };

    loadStoredAuth();
  }, []);
  
  // Proactive token refresh - check and refresh token before it expires
  useEffect(() => {
    if (!state.isAuthenticated) return;
    
    const checkAndRefreshToken = async () => {
      try {
        const tokens = JSON.parse(localStorage.getItem('wolffoxx_tokens') || '{}');
        if (!tokens.access_token) return;
        
        // Calculate when token will expire
        const expiresIn = tokens.expires_in || 18000; // Default to 5 hours (18000 seconds)
        const refreshedAt = tokens.refreshed_at ? new Date(tokens.refreshed_at).getTime() : Date.now();
        const expiresAt = refreshedAt + (expiresIn * 1000);
        const now = Date.now();
        
        // If token will expire in less than 30 minutes (1800 seconds), refresh it
        const refreshThreshold = 30 * 60 * 1000; // 30 minutes in milliseconds
        const timeUntilExpiry = expiresAt - now;
        
        if (timeUntilExpiry < refreshThreshold && timeUntilExpiry > 0) {
          // Use the authAPI directly to refresh the token
          const refreshToken = tokens.refresh_token || tokens.refreshToken;
          if (refreshToken) {
            try {
              const { authAPI } = await import('../services/authAPI');
              const newTokens = await authAPI.refreshToken(refreshToken);

              // Update localStorage with new tokens
              const updatedTokens = {
                ...tokens,
                ...newTokens,
                refreshed_at: new Date().toISOString()
              };
              localStorage.setItem('wolffoxx_tokens', JSON.stringify(updatedTokens));

            } catch (error) {
              // Proactive token refresh failed
            }
          }
        }
      } catch (error) {
        // Error in proactive token refresh
      }
    };
    
    // Check token every 30 seconds for better responsiveness
    const intervalId = setInterval(checkAndRefreshToken, 30000);
    
    // Run once on mount
    checkAndRefreshToken();
    
    return () => clearInterval(intervalId);
  }, [state.isAuthenticated]);

  // Listen for session expired events from apiFetch
  useEffect(() => {
    const handleSessionExpired = (event) => {
      
      // Show a user-friendly message
      if (state.isAuthenticated) {
        // Only show if user was previously authenticated
        showError(event.detail.message || 'Your session has expired. Please log in again.', 8000);
      }
      
      // Clear all user data
      clearAllUserData();
      
      // Update auth state
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    };
    
    // Add event listener
    window.addEventListener('auth:sessionExpired', handleSessionExpired);
    
    // Clean up
    return () => {
      window.removeEventListener('auth:sessionExpired', handleSessionExpired);
    };
  }, [state.isAuthenticated, showError]);

  // Save auth data to localStorage
  const saveAuthData = (user, tokens) => {
    try {
      console.log('💾 Saving auth data:', {
        hasUser: !!user,
        userId: user?.id,
        hasTokens: !!tokens
      });

      if (!user) {
        console.error('❌ Attempted to save null/undefined user data');
        return;
      }

      localStorage.setItem('wolffoxx_tokens', JSON.stringify(tokens));
      localStorage.setItem('wolffoxx_user', JSON.stringify(user));
      console.log('✅ Auth data saved successfully');
    } catch (error) {
      console.error('❌ Error saving auth data:', error);
    }
  };

  // Clear auth data from localStorage
  const clearAuthData = () => {
    localStorage.removeItem('wolffoxx_tokens');
    localStorage.removeItem('wolffoxx_user');
  };

  // Clear all user-specific data
  const clearAllUserData = () => {
    // Clear auth data
    clearAuthData();

    // Clear all localStorage data that might contain user-specific information
    localStorage.removeItem('cart');
    localStorage.removeItem('wishlist');
    localStorage.removeItem('outfits');
    localStorage.removeItem('currentOutfit');

    // Note: Context clear functions will be called separately
    // since we can't access other contexts from AuthContext directly
  };

  // Send OTP
  const sendOTP = async (phone) => {
    dispatch({ type: AUTH_ACTIONS.SEND_OTP_START });

    try {
      const response = await authAPI.sendOTP(phone);

      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_SUCCESS,
        payload: {
          phone: response.phone,
          originalPhone: phone // Store the original phone number
        }
      });

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Verify OTP and Login
  const verifyOTP = async (phone, otp, userDetails = {}) => {
    dispatch({ type: AUTH_ACTIONS.VERIFY_OTP_START });

    try {
      const response = await authAPI.verifyOTP(phone, otp, userDetails);

      const { user, tokens } = response;

      // Save to localStorage
      saveAuthData(user, tokens);

      dispatch({
        type: AUTH_ACTIONS.VERIFY_OTP_SUCCESS,
        payload: { user, tokens }
      });

      // Cart migration is now handled automatically by CartContext

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.VERIFY_OTP_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Resend OTP
  const resendOTP = async (phone) => {
    dispatch({ type: AUTH_ACTIONS.SEND_OTP_START });

    try {
      const response = await authAPI.resendOTP(phone);

      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_SUCCESS,
        payload: {
          phone: response.phone,
          originalPhone: phone // Store the original phone number
        }
      });

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.SEND_OTP_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Update Profile
  const updateProfile = async (profileData) => {
    dispatch({ type: AUTH_ACTIONS.UPDATE_PROFILE_START });

    try {
      const token = state.tokens?.accessToken || state.tokens?.access_token;
      console.log('🔄 Updating profile with token:', !!token);
      console.log('🔄 Current user before update:', state.user?.id);
      console.log('🔄 User auth method:', state.user?.auth_method);

      // Pass the user's auth method and user data to determine which endpoint to use
      const response = await authAPI.updateProfile(profileData, token, state.user?.auth_method, state.user);
      console.log('🔄 Profile update response:', {
        hasProfile: !!response.profile,
        profileId: response.profile?.id,
        success: response.success
      });

      // If no profile in response but update was successful, fetch the updated profile
      let updatedProfile = response.profile;
      if (!updatedProfile && response.success) {
        console.log('🔄 No profile in response, fetching updated profile...');
        try {
          const profileResponse = await authAPI.getProfile(token);
          updatedProfile = profileResponse;
          console.log('✅ Fetched updated profile:', { hasProfile: !!updatedProfile, profileId: updatedProfile?.id });
        } catch (fetchError) {
          console.error('❌ Failed to fetch updated profile:', fetchError);
          // Use current user data merged with the profile data as fallback
          updatedProfile = { ...state.user, ...profileData };
          console.log('🔄 Using merged profile data as fallback');
        }
      }

      // Ensure we have a valid profile before updating
      if (!updatedProfile) {
        console.error('❌ No profile available, using current user data');
        updatedProfile = state.user;
      }

      // Update stored user data
      saveAuthData(updatedProfile, state.tokens);
      console.log('✅ Profile data saved to localStorage');

      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE_SUCCESS,
        payload: { profile: updatedProfile }
      });

      console.log('✅ Profile update completed successfully');
      return {
        success: true,
        data: {
          ...response,
          profile: updatedProfile
        }
      };
    } catch (error) {
      console.error('❌ Profile update failed:', error);
      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  // Logout
  const logout = async () => {
    try {
      // Call logout API if needed
      const token = state.tokens?.accessToken || state.tokens?.access_token;
      if (token) {
        await authAPI.logout(token);
      }
    } catch (error) {
      // Logout API error
    } finally {
      // Clear all user data regardless of API call result
      clearAllUserData();
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Clear Error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Get fresh user profile
  const refreshProfile = async () => {
    const token = state.tokens?.accessToken || state.tokens?.access_token;
    if (!token) return;

    try {
      const response = await authAPI.getProfile(token);

      // Update stored user data
      saveAuthData(response, state.tokens);

      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE_SUCCESS,
        payload: { profile: response }
      });

      return { success: true, data: response };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Email/Password Authentication
  const emailLogin = async (credentials) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const response = await authAPI.login(credentials);

      // Save auth data
      saveAuthData(response.user, response.tokens || response);

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: { user: response.user, tokens: response.tokens || response }
      });

      // Cart migration is now handled automatically by CartContext

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  const emailRegister = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.LOGIN_START });

    try {
      const response = await authAPI.register(userData);

      // Save auth data
      saveAuthData(response.user, response.tokens || response);

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: { user: response.user, tokens: response.tokens || response }
      });

      return { success: true, data: response };
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: error.message }
      });
      return { success: false, error: error.message };
    }
  };

  const forgotPassword = async (email) => {
    const requestId = `ctx_forgot_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    console.log('🔗 AUTH_CONTEXT_FORGOT_PASSWORD_START', {
      requestId,
      email,
      timestamp: new Date().toISOString(),
      context: 'AuthContext'
    });

    try {
      console.log('🔗 AUTH_CONTEXT_FORGOT_PASSWORD_API_CALL', {
        requestId,
        email,
        callingAPI: 'authAPI.forgotPassword'
      });

      const response = await authAPI.forgotPassword(email);

      console.log('🔗 AUTH_CONTEXT_FORGOT_PASSWORD_SUCCESS', {
        requestId,
        email,
        responseKeys: Object.keys(response),
        success: true
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('🔗 AUTH_CONTEXT_FORGOT_PASSWORD_ERROR', {
        requestId,
        email,
        errorMessage: error.message,
        errorStack: error.stack,
        success: false
      });
      return { success: false, error: error.message };
    }
  };

  const resetPassword = async (token, password, confirmPassword) => {
    const requestId = `ctx_reset_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    console.log('🔗 AUTH_CONTEXT_RESET_PASSWORD_START', {
      requestId,
      tokenProvided: !!token,
      tokenLength: token ? token.length : 0,
      tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
      passwordProvided: !!password,
      passwordLength: password ? password.length : 0,
      confirmPasswordProvided: !!confirmPassword,
      passwordsMatch: password === confirmPassword,
      timestamp: new Date().toISOString(),
      context: 'AuthContext'
    });

    try {
      console.log('🔗 AUTH_CONTEXT_RESET_PASSWORD_API_CALL', {
        requestId,
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
        callingAPI: 'authAPI.resetPassword'
      });

      const response = await authAPI.resetPassword(token, password, confirmPassword);

      console.log('🔗 AUTH_CONTEXT_RESET_PASSWORD_SUCCESS', {
        requestId,
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
        responseKeys: Object.keys(response),
        success: true
      });

      return { success: true, data: response };
    } catch (error) {
      console.error('🔗 AUTH_CONTEXT_RESET_PASSWORD_ERROR', {
        requestId,
        tokenPrefix: token ? token.substring(0, 8) + '...' : 'not_provided',
        errorMessage: error.message,
        errorStack: error.stack,
        success: false
      });
      return { success: false, error: error.message };
    }
  };

  const value = {
    // State
    ...state,

    // Actions
    sendOTP,
    verifyOTP,
    resendOTP,
    updateProfile,
    logout,
    clearError,
    refreshProfile,

    // Email/Password Authentication
    emailLogin,
    emailRegister,
    forgotPassword,
    resetPassword,

    // Expose dispatch for direct state manipulation (use with caution)
    dispatch
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
