<?php
/**
 * Test script to verify OTP user creation works without email requirement
 */

require_once __DIR__ . '/vendor/autoload.php';

use Wolffoxx\Models\User;
use Wolffoxx\Config\Database;

try {
    echo "Testing OTP user creation without email...\n";
    
    // Initialize database connection
    Database::getInstance();
    
    $userModel = new User();
    
    // Test data for phone-based user creation
    $testPhone = '919999999999';
    $userData = [
        'phone' => $testPhone,
        'auth_method' => 'phone',
        'first_name' => null,
        'last_name' => null,
        'email' => null,
        'is_active' => true,
        'is_admin' => false,
        'newsletter_subscribed' => false,
        'marketing_emails' => true,
        'sms_notifications' => true
    ];
    
    // Clean up any existing test user
    $existing = $userModel->findBy('phone', $testPhone);
    if ($existing) {
        echo "Cleaning up existing test user...\n";
        $userModel->delete($existing['id']);
    }
    
    // Try to create user
    echo "Creating user with phone: $testPhone\n";
    $user = $userModel->create($userData);
    
    if ($user) {
        echo "✅ SUCCESS: User created successfully!\n";
        echo "User ID: " . $user['id'] . "\n";
        echo "Phone: " . $user['phone'] . "\n";
        echo "Auth Method: " . $user['auth_method'] . "\n";
        echo "Email: " . ($user['email'] ?? 'NULL') . "\n";
        
        // Clean up test user
        $userModel->delete($user['id']);
        echo "Test user cleaned up.\n";
    } else {
        echo "❌ FAILED: User creation failed\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
