-- Add OTP support to users table and create OTP table

-- Add auth_method column to users table
ALTER TABLE users
ADD COLUMN auth_method ENUM('email', 'phone') DEFAULT 'email' AFTER is_admin;

-- Make email nullable for phone-based auth
ALTER TABLE users
MODIFY COLUMN email VARCHAR(255) NULL;

-- Make password_hash nullable for phone-based auth
ALTER TABLE users
MODIFY COLUMN password_hash VARCHAR(255) NULL;

-- Add index for phone number
ALTER TABLE users
ADD INDEX idx_phone (phone);

-- Add index for auth_method
ALTER TABLE users
ADD INDEX idx_auth_method (auth_method);

-- Drop existing triggers that enforce email requirement
DROP TRIGGER IF EXISTS users_before_insert;
DROP TRIGGER IF EXISTS users_before_update;

-- Create new triggers that allow email to be NULL for phone-based auth
DELIMITER $$

CREATE TRIGGER users_before_insert
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    -- Only require email if auth_method is 'email'
    IF NEW.auth_method = 'email' AND (NEW.email IS NULL OR NEW.email = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Email is required for email authentication';
    END IF;

    -- Only require phone if auth_method is 'phone'
    IF NEW.auth_method = 'phone' AND (NEW.phone IS NULL OR NEW.phone = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Phone number is required for phone authentication';
    END IF;

    -- Always require first_name and last_name (but allow them to be set later via profile completion)
    -- Commenting out for now to allow user creation without names for OTP flow
    -- IF NEW.first_name IS NULL OR NEW.first_name = '' THEN
    --     SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'First name is required';
    -- END IF;

    -- IF NEW.last_name IS NULL OR NEW.last_name = '' THEN
    --     SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Last name is required';
    -- END IF;
END$$

CREATE TRIGGER users_before_update
BEFORE UPDATE ON users
FOR EACH ROW
BEGIN
    -- Only require email if auth_method is 'email'
    IF NEW.auth_method = 'email' AND (NEW.email IS NULL OR NEW.email = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Email is required for email authentication';
    END IF;

    -- Only require phone if auth_method is 'phone'
    IF NEW.auth_method = 'phone' AND (NEW.phone IS NULL OR NEW.phone = '') THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Phone number is required for phone authentication';
    END IF;

    -- Always require first_name and last_name (but allow them to be set later via profile completion)
    -- Commenting out for now to allow user creation without names for OTP flow
    -- IF NEW.first_name IS NULL OR NEW.first_name = '' THEN
    --     SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'First name is required';
    -- END IF;

    -- IF NEW.last_name IS NULL OR NEW.last_name = '' THEN
    --     SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Last name is required';
    -- END IF;
END$$

DELIMITER ;

-- Create OTP table for phone-based authentication
CREATE TABLE IF NOT EXISTS otps (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    otp VARCHAR(10) NOT NULL,
    purpose VARCHAR(50) NOT NULL DEFAULT 'login',
    attempts INT DEFAULT 0,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_phone (phone),
    INDEX idx_phone_purpose (phone, purpose),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create SMS logs table for tracking SMS delivery
CREATE TABLE IF NOT EXISTS sms_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    provider VARCHAR(50) NOT NULL,
    message_id VARCHAR(255),
    status ENUM('sent', 'delivered', 'failed', 'pending') DEFAULT 'pending',
    cost DECIMAL(10,4) DEFAULT 0.0000,
    
    -- Error tracking
    error_message TEXT,
    error_code VARCHAR(50),
    
    -- Timestamps
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    
    -- Indexes
    INDEX idx_phone (phone),
    INDEX idx_provider (provider),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_message_id (message_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create phone verification table for additional security
CREATE TABLE IF NOT EXISTS phone_verifications (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    phone VARCHAR(20) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP NULL,
    expires_at TIMESTAMP NOT NULL,
    attempts INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_phone (phone),
    INDEX idx_verification_code (verification_code),
    INDEX idx_is_verified (is_verified),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create authentication logs table for security tracking
CREATE TABLE IF NOT EXISTS auth_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    auth_method ENUM('email', 'phone', 'otp') NOT NULL,
    action ENUM('login_attempt', 'login_success', 'login_failed', 'logout', 'otp_sent', 'otp_verified', 'otp_failed') NOT NULL,
    
    -- Request details
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_type VARCHAR(50),
    browser VARCHAR(100),
    os VARCHAR(100),
    
    -- Location (optional)
    country VARCHAR(100),
    city VARCHAR(100),
    
    -- Security flags
    is_suspicious BOOLEAN DEFAULT FALSE,
    risk_score INT DEFAULT 0,
    
    -- Additional data
    metadata JSON,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_auth_method (auth_method),
    INDEX idx_action (action),
    INDEX idx_ip_address (ip_address),
    INDEX idx_is_suspicious (is_suspicious),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create rate limiting table for OTP requests
CREATE TABLE IF NOT EXISTS rate_limits (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL, -- phone, email, or IP
    action VARCHAR(100) NOT NULL, -- 'otp_request', 'login_attempt', etc.
    count INT DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    
    -- Indexes
    UNIQUE KEY unique_identifier_action (identifier, action),
    INDEX idx_expires_at (expires_at),
    INDEX idx_window_start (window_start)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default data for testing (optional)
-- You can remove this section in production

-- Create a test user with phone auth
INSERT IGNORE INTO users (
    uuid, 
    phone, 
    first_name, 
    last_name, 
    auth_method, 
    is_active, 
    is_admin,
    created_at
) VALUES (
    UUID(),
    '************', -- Test phone number
    'Test',
    'User',
    'phone',
    1,
    0,
    NOW()
);

-- Add some sample rate limit entries (will be cleaned up automatically)
INSERT IGNORE INTO rate_limits (
    identifier,
    action,
    count,
    expires_at
) VALUES (
    'system_init',
    'initialization',
    1,
    DATE_ADD(NOW(), INTERVAL 1 HOUR)
);
